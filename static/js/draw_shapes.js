
// Drawing canvas functionality for property and growing area shapes
let canvas, ctx;
let isDrawing = false;
let currentTool = 'free-draw';
let currentShape = [];
let shapes = [];
let showGrid = true;
let gridSize = 20;
let modeContext = 'property'; // 'property' or 'growingArea'
let propertyBoundary = null;

function initializeDrawingCanvas() {
    // Try multiple canvas IDs for compatibility
    canvas = document.getElementById('drawing-canvas') || document.getElementById('drawCanvas');
    if (!canvas) {
        console.error('Canvas element not found');
        return;
    }

    ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('Could not get canvas context');
        return;
    }

    // Set canvas size explicitly and ensure it's visible
    canvas.width = 800;
    canvas.height = 600;
    canvas.style.border = '2px solid #ccc';
    canvas.style.backgroundColor = '#ffffff';
    canvas.style.display = 'block';
    canvas.style.cursor = 'crosshair';

    console.log('Canvas initialized:', canvas.width, 'x', canvas.height);

    // Set up event listeners
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Tool selection
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentTool = this.id;
        });
    });

    // Clear button
    document.getElementById('clear').addEventListener('click', clearCanvas);

    // Toggle grid
    document.getElementById('toggle-grid').addEventListener('click', toggleGrid);

    // Initial draw
    clearCanvas();
}

function startDrawing(e) {
    isDrawing = true;
    const rect = canvas.getBoundingClientRect();
    const x = Math.round((e.clientX - rect.left) / gridSize) * gridSize;
    const y = Math.round((e.clientY - rect.top) / gridSize) * gridSize;

    currentShape = [];

    if (currentTool === 'free-draw') {
        currentShape.push({ x, y });
    } else if (currentTool === 'rectangle') {
        currentShape.push({ x, y }); // Starting point
    } else if (currentTool === 'circle') {
        currentShape.push({ x, y }); // Center point
    }

    redrawCanvas();
}

function draw(e) {
    if (!isDrawing) return;

    const rect = canvas.getBoundingClientRect();
    const x = Math.round((e.clientX - rect.left) / gridSize) * gridSize;
    const y = Math.round((e.clientY - rect.top) / gridSize) * gridSize;

    if (currentTool === 'free-draw') {
        // Add point to current shape
        currentShape.push({ x, y });
    } else if (currentTool === 'rectangle' || currentTool === 'circle') {
        // Update the second point (for preview)
        if (currentShape.length > 1) {
            currentShape[1] = { x, y };
        } else {
            currentShape.push({ x, y });
        }
    }

    redrawCanvas();
}

function stopDrawing() {
    if (!isDrawing) return;
    isDrawing = false;

    if (currentShape.length < 2) return;

    if (currentTool === 'rectangle') {
        // Convert rectangle to polygon points
        const startX = currentShape[0].x;
        const startY = currentShape[0].y;
        const endX = currentShape[1].x;
        const endY = currentShape[1].y;

        currentShape = [
            { x: startX, y: startY },
            { x: endX, y: startY },
            { x: endX, y: endY },
            { x: startX, y: endY },
            { x: startX, y: startY } // Close the shape
        ];
    } else if (currentTool === 'circle') {
        // Convert circle to polygon points
        const centerX = currentShape[0].x;
        const centerY = currentShape[0].y;
        const radius = Math.sqrt(
            Math.pow(currentShape[1].x - centerX, 2) +
            Math.pow(currentShape[1].y - centerY, 2)
        );

        const segments = 36; // Number of segments to approximate circle
        currentShape = [];

        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            currentShape.push({ x: Math.round(x / gridSize) * gridSize, y: Math.round(y / gridSize) * gridSize });
        }
    }

    // Add the shape to our collection
    shapes.push({
        type: modeContext === 'property' ? 'boundary' : 'growingArea',
        points: currentShape
    });

    currentShape = [];
    redrawCanvas();
}

function clearCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (showGrid) {
        drawGrid();
    }

    // If we're in growing area mode, draw the property boundary first
    if (modeContext === 'growingArea' && propertyBoundary) {
        drawPropertyBoundary();
    }

    // Draw all shapes
    shapes.forEach(shape => {
        drawShape(shape.points, shape.type === 'boundary' ? '#3b82f6' : '#10b981');
    });

    // Draw current shape
    if (currentShape.length > 0) {
        if (currentTool === 'rectangle' && currentShape.length === 2) {
            // Preview rectangle
            const startX = currentShape[0].x;
            const startY = currentShape[0].y;
            const endX = currentShape[1].x;
            const endY = currentShape[1].y;

            ctx.beginPath();
            ctx.rect(startX, startY, endX - startX, endY - startY);
            ctx.strokeStyle = modeContext === 'property' ? '#3b82f6' : '#10b981';
            ctx.lineWidth = 2;
            ctx.stroke();
        } else if (currentTool === 'circle' && currentShape.length === 2) {
            // Preview circle
            const centerX = currentShape[0].x;
            const centerY = currentShape[0].y;
            const radius = Math.sqrt(
                Math.pow(currentShape[1].x - centerX, 2) +
                Math.pow(currentShape[1].y - centerY, 2)
            );

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.strokeStyle = modeContext === 'property' ? '#3b82f6' : '#10b981';
            ctx.lineWidth = 2;
            ctx.stroke();
        } else {
            // Draw free-form shape
            drawShape(currentShape, modeContext === 'property' ? '#3b82f6' : '#10b981');
        }
    }
}

function drawShape(points, color) {
    if (points.length < 2) return;

    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);

    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }

    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();

    // Fill with semi-transparent color
    ctx.fillStyle = color + '40'; // 25% opacity
    ctx.fill();
}

function drawGrid() {
    ctx.beginPath();
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 0.5;

    // Draw vertical lines
    for (let x = 0; x <= canvas.width; x += gridSize) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
    }

    // Draw horizontal lines
    for (let y = 0; y <= canvas.height; y += gridSize) {
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
    }

    ctx.stroke();
}

function toggleGrid() {
    showGrid = !showGrid;
    redrawCanvas();
}

function redrawCanvas() {
    clearCanvas();
}

function loadPropertyBoundary(boundary) {
    propertyBoundary = boundary;
    redrawCanvas();
}

function drawPropertyBoundary() {
    propertyBoundary.forEach(shape => {
        drawShape(shape.points, '#3b82f6');
    });
}

function getShapesData() {
    return shapes;
}

function calculateArea(points) {
    let area = 0;
    for (let i = 0; i < points.length - 1; i++) {
        area += points[i].x * points[i + 1].y - points[i + 1].x * points[i].y;
    }
    return Math.abs(area / 2);
}

// Global initialization function for templates
function initDrawing(mode, existingShapes, showGridDefault) {
    modeContext = mode || 'property';
    showGrid = showGridDefault !== undefined ? showGridDefault : true;

    if (existingShapes && Array.isArray(existingShapes)) {
        shapes = existingShapes;
    }

    initializeDrawingCanvas();
}

// Auto-initialize if canvas is found on page load
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('drawing-canvas') || document.getElementById('drawCanvas')) {
        initializeDrawingCanvas();
    }
});
