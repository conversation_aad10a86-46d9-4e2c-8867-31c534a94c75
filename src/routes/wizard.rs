// src/routes/wizard.rs
use crate::models::{
    property_shape::{NewPropertyShape, PropertyShape},
    growing_area_shape::{NewGrowingAreaShape, GrowingAreaShape},
    household::NewHousehold,
    property::Property,
};
use crate::schema::{property_shapes, growing_area_shapes, households};
use crate::utils::templates::{render_template, render_template_with_context};
use crate::utils::csrf::verify_csrf_token;
use crate::DbPool;
use actix_web::{web, HttpResponse, Result};
use actix_session::Session;
use diesel::prelude::*;
use serde::Deserialize;
use tera::Context;


#[derive(Deserialize)]
pub struct PropertyShapeForm {
    pub shape_data: String,
    pub shape_type: String,
    pub area: Option<f64>,
    pub floor_no: i32,
}

// -------------------
// STEP 0: Start Wizard
// -------------------

pub async fn start_wizard(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }
    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/household"))
        .finish())
}

// -------------------
// STEP 1: Household
// -------------------

#[derive(Deserialize)]
pub struct HouseholdForm {
    pub name: String,
    pub csrf_token: String,
}

pub async fn create_household_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    let mut ctx = Context::new();
    if let Some(u) = session.get::<String>("username")? {
        ctx.insert("username", &u);
    }
    render_template_with_context("wizard/create_household.html", &mut ctx, &session)
}

pub async fn create_household(
    session: Session,
    form: web::Form<HouseholdForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if let Some(uid) = session.get::<i32>("user_id")? {
        let mut conn = pool.get().expect("DB connection failed");
        let new_hh = NewHousehold {
            name: &form.name,
            owner_id: uid,
        };
        diesel::insert_into(households::table)
            .values(&new_hh)
            .execute(&mut conn)
            .expect("Error inserting household");
        Ok(HttpResponse::Found()
            .append_header(("Location", "/wizard/invite"))
            .finish())
    } else {
        Ok(HttpResponse::Unauthorized().finish())
    }
}

// -------------------
// STEP 2: Invite Members
// -------------------

#[derive(Deserialize)]
pub struct InviteMembersForm {
    pub emails: String, // Comma‐separated list
}

pub async fn invite_members_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    let mut ctx = Context::new();
    if let Some(u) = session.get::<String>("username")? {
        ctx.insert("username", &u);
    }
    render_template_with_context("wizard/invite_members.html", &mut ctx, &session)
}

pub async fn invite_members(
    session: Session,
    _form: web::Form<InviteMembersForm>,
    _pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    // (For now, skipping invitation processing)
    if session.get::<String>("username")?.is_some() {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/wizard/property"))
            .finish())
    } else {
        Ok(HttpResponse::Unauthorized().finish())
    }
}

// -------------------
// STEP 3: Create Property
// -------------------

#[derive(Deserialize)]
pub struct PropertyForm {
    pub name: String,
    pub outside_area: Option<i32>, // Default to 0 if empty
    pub inside_area: Option<i32>,  // Default to 0 if empty
    pub floors: Option<i32>,       // Default to 0 if empty
}

pub async fn create_property_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    let mut ctx = Context::new();
    if let Some(u) = session.get::<String>("username")? {
        ctx.insert("username", &u);
    }
    render_template_with_context("wizard/create_property.html", &mut ctx, &session)
}

pub async fn create_property(
    session: Session,
    form: web::Form<PropertyForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }

    let outside_area_val = form.outside_area.unwrap_or(0) as f32;
    let inside_area_val = form.inside_area.unwrap_or(0) as f32;
    let floors_val = form.floors.unwrap_or(1);

    // Save the property to the database
    let mut conn = pool.get().expect("DB connection failed");

    // Check if a property already exists
    use crate::schema::properties::dsl::*;
    let existing_property = properties
        .first::<Property>(&mut conn)
        .optional()
        .expect("Error checking for existing property");

    let property_id = if let Some(prop) = existing_property {
        // Property exists, use its ID
        prop.id.unwrap_or(1)
    } else {
        // Create a new property
        use crate::models::property::NewProperty;

        // Get the user ID from the session
        let user_id = session.get::<i32>("user_id")?.unwrap_or(1);

        // Get the household ID (using the first household for simplicity)
        let user_household_id = crate::schema::households::dsl::households
            .filter(crate::schema::households::dsl::owner_id.eq(user_id))
            .select(crate::schema::households::dsl::id)
            .first::<i32>(&mut conn)
            .unwrap_or(1); // Default to 1 if no household found

        let new_property = NewProperty {
            name: &form.name,
            inside_area: Some(inside_area_val),
            outside_area: Some(outside_area_val),
            floors: floors_val,
            owner_id: user_id,
            household_id: user_household_id,
        };

        diesel::insert_into(crate::schema::properties::table)
            .values(&new_property)
            .execute(&mut conn)
            .expect("Error inserting property");

        // Get the ID of the newly inserted property
        properties
            .select(id)
            .order(id.desc())
            .first::<Option<i32>>(&mut conn)
            .expect("Error getting property ID")
            .unwrap_or(1)
    };

    // Store property info in session
    session.insert("property_id", &property_id)?;
    session.insert("property_name", &form.name)?;
    session.insert("outside_area", &outside_area_val)?;
    session.insert("inside_area", &inside_area_val)?;
    session.insert("floors", &floors_val)?;

    // Use floor index: if outside_area > 0, use floor = -1 for outside; otherwise, start at 0
    let start_floor = if outside_area_val > 0.0 { -1 } else { 0 };
    session.insert("current_floor", &start_floor)?;

    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/draw_property_shapes"))
        .finish())
}

// -------------------
// STEP 4: Draw Property Shapes
// -------------------

#[derive(Deserialize)]
pub struct PropertyShapeSaveForm {
    pub shape_data: String,
    pub shape_type: String,
    pub area: Option<f64>,
    pub floor_no: i32,
}

#[derive(Deserialize)]
pub struct FinishFloorForm {
    pub floor_no: i32,
}

pub async fn get_property_shapes(
    pool: web::Data<DbPool>,
    property_id: web::Path<i32>,
    floor_no: web::Path<i32>,
) -> Result<HttpResponse, actix_web::Error> {
    let mut conn = pool.get().expect("couldn't get db connection from pool");
    let shapes = property_shapes::table
        .filter(property_shapes::property_id.eq(*property_id))
        .filter(property_shapes::floor_no.eq(*floor_no))
        .select(PropertyShape::as_select())
        .load(&mut conn)
        .expect("Error loading property shapes");

    Ok(HttpResponse::Ok().json(shapes))
}

pub async fn save_property_shape(
    session: Session,
    form: web::Form<PropertyShapeForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }

    let property_id = session.get::<i32>("property_id")?.unwrap_or(1);
    let mut conn = pool.get().expect("couldn't get db connection from pool");

    let new_shape = NewPropertyShape {
        property_id,
        floor_no: form.floor_no,
        shape_data: &form.shape_data,
        shape_type: &form.shape_type,
        area: form.area,
    };

    diesel::insert_into(property_shapes::table)
        .values(&new_shape)
        .execute(&mut conn)
        .expect("Error saving new property shape");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/draw_property_shapes"))
        .finish())
}

pub async fn finish_floor(
    session: Session,
    form: web::Form<FinishFloorForm>,
    _pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }

    let curr_floor = form.floor_no;
    let floors = session.get::<i32>("floors")?.unwrap_or(0);

    // If we have more floors to process
    if curr_floor < floors - 1 {  // Changed condition to floors - 1
        session.insert("current_floor", curr_floor + 1)?;
        Ok(HttpResponse::Found()
            .append_header(("Location", "/wizard/draw_property_shapes"))
            .finish())
    } else {
        // All floors are done, move to growing areas
        session.insert("current_grow_floor", -1)?;  // Start with outside
        Ok(HttpResponse::Found()
            .append_header(("Location", "/wizard/draw_growing_areas"))
            .finish())
    }
}

// -------------------
// STEP 5: Draw Growing Areas
// -------------------

#[derive(Deserialize)]
pub struct GrowingAreaShapeForm {
    pub shape_data: String,
    pub shape_type: String,
    pub floor_no: i32,
    pub area: Option<f32>,  // Add area field
}

pub async fn draw_growing_areas_form(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }
    let mut ctx = Context::new();
    let gf = session.get::<i32>("current_grow_floor")?.unwrap_or(-1);
    let floors = session.get::<i32>("floors")?.unwrap_or(0);
    let display_floor = if gf < 0 {
        "Outside".to_string()
    } else {
        format!("Floor {}", gf)
    };
    ctx.insert("display_floor", &display_floor);
    ctx.insert("current_floor", &gf);
    ctx.insert("floors", &floors);

    let property_id = session.get::<i32>("property_id")?.unwrap_or(1);
    let mut conn = pool.get().expect("DB connection failed");

    // Load property shapes for boundaries
    let property_shapes = property_shapes::table
        .filter(property_shapes::property_id.eq(property_id))
        .select(PropertyShape::as_select())
        .load(&mut conn)
        .unwrap_or_default();

    let boundary_json = serde_json::to_string(&property_shapes)
        .unwrap_or_else(|_| "[]".to_string());

    ctx.insert("property_boundary_shapes", &boundary_json);

    // If no property boundary shapes, skip drawing growing areas for this floor.
    if property_shapes.is_empty() {
        return finish_grow_floor(session).await;
    }

    // Load existing growing area shapes
    let existing_ga = growing_area_shapes::table
        .filter(growing_area_shapes::property_id.eq(property_id))
        .filter(growing_area_shapes::floor_no.eq(gf))
        .select(GrowingAreaShape::as_select())
        .load(&mut conn)
        .unwrap_or_default();

    // Serialize the shapes to JSON string
    let shapes_json = serde_json::to_string(&existing_ga)
        .unwrap_or_else(|_| "[]".to_string());

    ctx.insert("existing_growing_shapes", &shapes_json);

    render_template_with_context("wizard/draw_growing_areas.html", &mut ctx, &session)
}

pub async fn save_growing_area_shape(
    session: Session,
    form: web::Form<GrowingAreaShapeForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }
    let property_id = session.get::<i32>("property_id")?.unwrap_or(1);
    let mut conn = pool.get().expect("DB connection failed");
    let newga = NewGrowingAreaShape {
        property_id,
        shape_data: &form.shape_data,
        shape_type: &form.shape_type,
        floor_no: form.floor_no,
        area: form.area,
    };
    diesel::insert_into(growing_area_shapes::table)
        .values(&newga)
        .execute(&mut conn)
        .expect("Error inserting growing area shape");
    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/draw_growing_areas"))
        .finish())
}

pub async fn finish_grow_floor(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }
    let mut gf = session.get::<i32>("current_grow_floor")?.unwrap_or(-1);
    let floors = session.get::<i32>("floors")?.unwrap_or(0);
    gf = if gf < 0 { 0 } else { gf + 1 };
    session.insert("current_grow_floor", &gf)?;
    if gf >= floors {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/wizard/finish_wizard"))
            .finish());
    }
    Ok(HttpResponse::Found()
        .append_header(("Location", "/wizard/draw_growing_areas"))
        .finish())
}

// -------------------
// STEP 6: Finish Wizard
// -------------------

pub async fn finish_wizard(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }
    Ok(HttpResponse::Found()
        .append_header(("Location", "/property"))
        .finish())
}

pub async fn draw_property_shapes_form(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_none() {
        return Ok(HttpResponse::Unauthorized().finish());
    }

    let mut ctx = Context::new();
    let current_floor = session.get::<i32>("current_floor")?.unwrap_or(0);
    let floors = session.get::<i32>("floors")?.unwrap_or(0);
    let display_floor = if current_floor < 0 {
        "Outside".to_string()
    } else {
        format!("Floor {}", current_floor)
    };

    ctx.insert("display_floor", &display_floor);
    ctx.insert("current_floor", &current_floor);
    ctx.insert("floors", &floors);

    // Add area values for scaling the drawing area
    let outside_area = session.get::<f32>("outside_area")?.unwrap_or(0.0);
    let inside_area = session.get::<f32>("inside_area")?.unwrap_or(0.0);
    ctx.insert("outside_area", &outside_area);
    ctx.insert("inside_area", &inside_area);

    // Load existing shapes for this floor
    let property_id = session.get::<i32>("property_id")?.unwrap_or(1);
    let mut conn = pool.get().expect("couldn't get db connection from pool");

    let existing_shapes = property_shapes::table
        .filter(property_shapes::property_id.eq(property_id))
        .filter(property_shapes::floor_no.eq(current_floor))
        .select(PropertyShape::as_select())
        .load(&mut conn)
        .unwrap_or_default();

    let shapes_json = serde_json::to_string(&existing_shapes)
        .unwrap_or_else(|_| "[]".to_string());

    ctx.insert("existing_shapes", &shapes_json);

    render_template_with_context("wizard/draw_property.html", &mut ctx, &session)
}

// -------------------
// Register routes for the wizard
// -------------------

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/wizard")
            .route("", web::get().to(start_wizard))
            .route("/start", web::get().to(start_wizard))
            .route("/household", web::get().to(create_household_form))
            .route("/household", web::post().to(create_household))
            .route("/invite", web::get().to(invite_members_form))
            .route("/invite", web::post().to(invite_members))
            .route("/property", web::get().to(create_property_form))
            .route("/property", web::post().to(create_property))
            .route("/draw_property_shapes", web::get().to(draw_property_shapes_form))
            .route("/save_property_shape", web::post().to(save_property_shape))
            .route("/finish_floor", web::post().to(finish_floor))
            .route("/draw_growing_areas", web::get().to(draw_growing_areas_form))
            .route("/save_growing_area_shape", web::post().to(save_growing_area_shape))
            .route("/finish_grow_floor", web::get().to(finish_grow_floor))
            .route("/finish_wizard", web::get().to(finish_wizard)),
    );
}
