use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::models::user::User;
use crate::models::plant::Plant;
use crate::schema::{users, plants};
use crate::utils::templates::{render_template, render_template_with_context};
use crate::utils::auth::is_authenticated;
use crate::DbPool;

pub async fn admin_dashboard(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if is_admin {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        // Get user statistics
        let total_users = users::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

        let mut ctx = tera::Context::new();
        ctx.insert("total_users", &total_users);
        ctx.insert("total_plants", &total_plants);
        ctx.insert("user_role", &user_role);

        render_template_with_context("admin/dashboard.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."))
    }
}

// Add a simple admin index route
pub async fn admin_index(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse, actix_web::Error> {
    admin_dashboard(session, pool).await
}

// User management
pub async fn list_users(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_users = users::table
        .load::<User>(&mut conn)
        .expect("Error loading users");

    let mut ctx = tera::Context::new();
    ctx.insert("users", &all_users);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/list.html", &mut ctx, &session)
}

#[derive(Deserialize)]
pub struct UpdateUserRoleForm {
    pub user_id: i32,
    pub role: String,
}

pub async fn update_user_role(
    session: Session,
    form: web::Form<UpdateUserRoleForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_superadmin = user_role == "superadmin";

    if !is_superadmin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: Only superadmin can change user roles."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    diesel::update(users::table.find(form.user_id))
        .set(users::role.eq(&form.role))
        .execute(&mut conn)
        .expect("Error updating user role");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/admin/users"))
        .finish())
}

// HerbaDB management
pub async fn manage_herba_db(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_plants = plants::table
        .load::<Plant>(&mut conn)
        .expect("Error loading plants");

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/herba_database.html", &mut ctx, &session)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/admin")
            .route("", web::get().to(admin_index))
            .route("/dashboard", web::get().to(admin_dashboard))
            .route("/users", web::get().to(list_users))
            .route("/users/update-role", web::post().to(update_user_role))
            .route("/herba-db", web::get().to(manage_herba_db)),
    );
}
